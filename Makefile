# HGSTExplorer 重构项目 Makefile
# 自动生成于: 2025-07-30 14:55:44

CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -Iinclude
SRCDIR = src
OBJDIR = obj
TARGET = hgstexplorer

SOURCES = $(SRCDIR)/core.c $(SRCDIR)/ui.c $(SRCDIR)/utils.c $(SRCDIR)/main.c main.c
OBJECTS = $(SOURCES:%.c=$(OBJDIR)/%.o)

all: $(TARGET)

$(TARGET): $(OBJECTS)
	$(CC) $(OBJECTS) -o $@

$(OBJDIR)/%.o: %.c
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) -c $< -o $@

clean:
	rm -rf $(OBJDIR) $(TARGET)

.PHONY: all clean
