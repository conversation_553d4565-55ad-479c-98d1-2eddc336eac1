/*
 * UI 模块实现
 * 用户界面模块
 * 自动生成于: 2025-07-30 14:55:44
 */

#include "../include/ui.h"
#include "../include/utils.h"
#include "../include/core.h"

/*
 * 函数: sub_53DD50
 * 地址: 0x0053DD50
 * 类型: ui_operation
 * 复杂度: 1912
 */
HWND sub_53DD50(HWND parent)
{
    // 字符串常量
    const char* str_0 = "UWVS";
    const char* str_1 = "UWVS";
    const char* str_2 = "UWVS";

    // 局部变量
    int result = 0;
    DWORD dwResult = 0;

    // API调用
    NtdllDefWindowProc_W();

    // 内部函数调用
    sub_9862E0();
    sub_9AB2B0();
    sub_449780();
    SendMessageW();
    ClientToScreen();
    sub_4478A0();
    sub_466E40();
    sub_466F50();
    sub_45E560();
    sub_768CE0();
    sub_768CE0();
    sub_9825F0();
    sub_9AABE0();
    sub_4478A0();
    sub_448030();
    sub_45E560();
    sub_5371E0();
    sub_4478A0();
    sub_448030();
    sub_45E560();
    sub_5364E0();
    ReleaseCapture();
    sub_44A2B0();
    sub_47FBA0();
    SetCursor();
    sub_45E560();
    sub_45F8D0();
    GetFocus();
    sub_45F050();
    sub_45DB90();
    sub_45F9B0();
    sub_449780();
    sub_4478B0();
    sub_466E40();
    sub_466F50();
    sub_53C530();
    sub_9AAA50();
    sub_4478A0();
    sub_B27020();
    sub_44CA00();
    sub_B27020();
    sub_44CA00();
    sub_44CA00();
    sub_53B8F0();
    sub_B27020();
    sub_460E10();
    PeekMessageW();
    sub_4BB820();
    sub_9AA7C0();
    sub_4BA2C0();
    sub_55F9B0();
    sub_76AE30();
    PeekMessageW();
    sub_50FF90();
    sub_50F500();
    sub_50F500();
    sub_466900();
    sub_55F9B0();
    sub_76B180();
    _flushall();
    sub_4478A0();
    sub_45E560();
    sub_5367D0();
    sub_9CE340();
    sub_ACC4B0();
    sub_9CE160();
    sub_50FF90();
    sub_B27020();
    sub_98EFA0();
    sub_98DA70();
    sub_539860();
    sub_50F6E0();
    sub_4BB900();
    sub_448500();
    sub_4BA1D0();
    sub_447D90();
    sub_45D990();
    sub_9812A0();
    sub_5360C0();
    sub_4496F0();
    sub_B02CB0();
    sub_B02FE0();
    sub_B02FE0();
    sub_45F050();
    sub_60E030();
    sub_4BBA60();
    sub_ACC4B0();
    sub_4BA200();
    sub_53B940();
    sub_539D50();
    sub_4BC0E0();
    sub_448500();
    sub_4BA4A0();
    sub_4478A0();
    sub_449780();
    sub_45E690();
    sub_539980();
    sub_536350();
    sub_449BB0();
    sub_B27020();
    sub_45F050();
    sub_B27020();
    sub_50FFA0();
    sub_9CE340();
    sub_ACC4B0();
    sub_9CE160();
    sub_45F050();
    sub_45F050();
    sub_45FA70();
    sub_460280();
    sub_460470();
    sub_460470();
    sub_461690();
    sub_5013F0();
    sub_449780();
    SetCapture();
    sub_44D710();
    sub_449780();
    sub_46BAE0();
    sub_449780();
    GetLocaleInfoW();
    sub_55FB70();
    sub_50F820();
    sub_461690();
    sub_45E670();
    sub_45E890();
    sub_45E8B0();
    sub_647DE0();
    sub_968E60();
    sub_96DB00();
    memcpy();
    sub_B06D40();
    ReleaseCapture();
    sub_536440();
    sub_50FF90();
    sub_B27020();
    sub_76B100();
    sub_B06D40();
    sub_539B30();
    sub_B27020();
    sub_47F210();
    sub_9D90B0();
    sub_45E560();
    sub_44DF70();
    sub_98BD20();
    sub_53D6A0();
    sub_53B000();
    sub_447D90();
    sub_45D990();
    sub_9812A0();
    sub_769860();
    sub_767FF0();
    sub_767FF0();
    GetDC();
    sub_767FF0();
    ReleaseDC();
    sub_9AAA40();
    sub_9AAA50();
    sub_647DE0();
    sub_55E6D0();
    dword_EF1874();
    sub_461690();
    sub_98BD20();
    sub_46B690();
    sub_53D3D0();
    sub_5C4AC0();
    GetLastError();
    sub_B6B9D0();
    sub_9DE930();
    sub_96D190();
    sub_9E4EE0();
    j_j_free_2();
    sub_9E4540();
    sub_9E4A10();
    sub_9E4540();
    sub_B069B0();
    dword_EF2D10();
    dword_EF2D10();
    sub_B6F370();
    sub_5366F0();
    InvalidateRect();
    sub_96D310();
    sub_96E7C0();
    sub_B06D40();
    sub_B27020();
    sub_4BC110();
    sub_448500();
    sub_4BA4D0();
    sub_B27020();
    sub_4BBE80();
    sub_448500();
    sub_46B030();
    sub_4BA410();
    sub_45E670();
    sub_45E670();
    sub_4BB820();
    sub_ACC4B0();
    sub_449780();
    ReleaseCapture();
    sub_96D310();
    sub_96A5B0();
    sub_B06D40();
    sub_B27020();
    sub_96CF10();
    sub_968FA0();
    sub_B06D40();
    sub_96DB00();
    sub_A0A720();
    GetProcAddress();
    sub_B06D40();
    sub_449780();
    sub_449780();
    sub_45FA70();
    sub_449780();
    sub_45FA70();
    sub_45E560();
    sub_449780();
    sub_46BAE0();
    sub_449780();
    sub_46C130();
    sub_4BBEA0();
    sub_448500();
    sub_463290();
    sub_45F590();
    sub_B06D40();
    sub_4BA440();
    sub_5366F0();
    sub_50F820();
    sub_460A20();
    dword_EF2D08();
    sub_9CFAC0();
    sub_B27020();
    sub_9CE340();
    sub_ACC4B0();
    sub_9CE160();
    sub_769470();
    sub_769300();
    sub_768D60();
    sub_45E890();
    sub_45E8B0();
    sub_45DB90();
    sub_4BBEA0();
    sub_448500();
    sub_46C130();
    sub_4BA440();
    sub_50FF80();
    sub_5503F0();
    GetWindowRgn();
    sub_50F820();
    PtInRegion();
    DeleteObject();
    sub_461CF0();
    sub_50F6E0();
    sub_986220();
    sub_968FA0();
    sub_B06D40();
    sub_50FF80();
    sub_44DF70();
    sub_45E5B0();
    sub_98BD20();
    sub_4BBE80();
    sub_448500();
    sub_46B030();
    sub_45F6D0();
    sub_B06D40();
    sub_4BA410();
    sub_448030();
    sub_45E560();
    sub_536180();
    sub_44DF70();
    sub_4478B0();
    sub_447D90();
    sub_447D90();
    sub_50FFA0();
    sub_45F050();
    sub_45F050();
    IsChild();
    sub_9D0240();
    sub_465240();
    sub_447D90();
    GetFocus();
    dword_EF2D00();
    dword_EF2D10();
    dword_EF2D10();
    dword_EF2D10();
    sub_B6F370();
    sub_AF0650();
    sub_449780();
    sub_449780();
    sub_45FA70();
    sub_449780();
    sub_45FA70();
    sub_449780();
    sub_447D90();
    sub_447D90();
    sub_449780();
    sub_447D90();
    sub_447D90();
    sub_45FA80();
    sub_460280();
    sub_45F050();
    sub_B27020();
    sub_45FA70();
    SetWindowPos();
    sub_45F590();
    GetFocus();
    sub_45F050();
    sub_45F050();

    return result;
}

/*
 * 函数: sub_768CE0
 * 地址: 0x00768CE0
 * 类型: ui_operation
 * 复杂度: 13
 */
HWND sub_768CE0(HWND parent)
{
    // 局部变量
    int result = 0;
    DWORD dwResult = 0;

    // API调用
    NtdllDefWindowProc_W();

    return result;
}

/*
 * 函数: sub_A0D760
 * 地址: 0x00A0D760
 * 类型: ui_operation
 * 复杂度: 78
 */
HWND sub_A0D760(HWND parent)
{
    // 局部变量
    int result = 0;
    DWORD dwResult = 0;

    // API调用
    NtdllDefWindowProc_W();

    // 内部函数调用
    sub_9AB2B0();
    GetWindowLongW();
    KillTimer();
    sub_A0AF70();
    sub_9CE340();
    sub_9AA7C0();
    sub_9CE160();
    sub_9ACDE0();
    sub_A0D680();

    return result;
}

