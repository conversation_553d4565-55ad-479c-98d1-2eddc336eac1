/*
 * HGSTExplorer 重构项目主文件
 * 自动生成于: 2025-07-30 14:55:44
 */

#include "include/core.h"
#include "include/ui.h"
#include "include/utils.h"
#include "include/main.h"

int main(int argc, char* argv[])
{
    // 程序初始化
    printf("HGSTExplorer 启动中...\n");

    // 调用原始入口点: start
    start(argc, argv);
    // 调用原始入口点: _WinMain@16
    _WinMain@16(argc, argv);
    // 调用原始入口点: StartAddress
    StartAddress(argc, argv);
    // 调用原始入口点: __getmainargs
    __getmainargs(argc, argv);

    return 0;
}
