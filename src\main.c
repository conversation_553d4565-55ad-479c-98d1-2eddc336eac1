/*
 * MAIN 模块实现
 * 主程序模块
 * 自动生成于: 2025-07-30 14:55:44
 */

#include "../include/main.h"
#include "../include/utils.h"
#include "../include/core.h"

/*
 * 函数: start
 * 地址: 0x00401180
 * 类型: entry_point
 * 复杂度: 125
 */
int start(int argc, char* argv[])
{
    // 字符串常量
    const char* str_0 = "_set_invalid_parameter_handler";

    // 局部变量
    int result = 0;
    DWORD dwResult = 0;

    // 内部函数调用
    sub_A8C480();
    sub_A7A3D0();
    sub_446320();
    SetUnhandledExceptionFilter();
    sub_4468B0();
    GetProcAddress();
    sub_446970();
    malloc();
    strlen();
    malloc();
    memcpy();
    sub_446A00();
    sub_B6F260();
    _cexit();
    _amsg_exit();
    _initterm();
    GetStartupInfoA();
    _initterm();
    exit();
    sub_446A20();

    return result;
}

/*
 * 函数: _WinMain@16
 * 地址: 0x00446DC0
 * 类型: entry_point
 * 复杂度: 65
 */
int _WinMain@16(int argc, char* argv[])
{
    // 字符串常量
    const char* str_0 = "UWVS";

    // 局部变量
    int result = 0;
    DWORD dwResult = 0;

    // 内部函数调用
    GetCommandLineW();
    sub_96D310();
    sub_96CE60();
    j_j_free_2();
    sub_9C9E50();
    j_memset_0();
    sub_961880();
    sub_9CD340();
    sub_B1AE80();
    sub_401600();
    sub_9C9EA0();
    j_free_2();

    return result;
}

/*
 * 函数: StartAddress
 * 地址: 0x009A01D0
 * 类型: entry_point
 * 复杂度: 54
 */
int StartAddress(int argc, char* argv[])
{
    // 字符串常量
    const char* str_0 = "UWVS";
    const char* str_1 = "UWVS";
    const char* str_2 = "UWVS";

    // 局部变量
    int result = 0;
    DWORD dwResult = 0;

    // 内部函数调用
    TlsSetValue();
    GetCurrentThreadId();
    sub_9A00B0();
    sub_9CDE60();
    sub_99F700();
    sub_A35CF0();
    sub_9A00B0();
    sub_99F7E0();
    sub_9CE0D0();
    sub_9CE150();
    sub_99F5B0();
    sub_9CDE10();

    return result;
}

/*
 * 函数: __getmainargs
 * 地址: 0x00A9F2B0
 * 类型: entry_point
 * 复杂度: 0
 */
int __getmainargs(int argc, char* argv[])
{
    // 局部变量
    int result = 0;
    DWORD dwResult = 0;

    return result;
}

